#import "style.typ": *

#let fa_envelop = draw_font_icon("\u{f0e0}", color: color_light_gray)
#let fa_phone = draw_font_icon("\u{f095}", color: color_light_gray)
#let fa_map_marker = draw_font_icon("\u{f3c5}", color: color_light_gray)
#let fa_rss = draw_font_icon("\u{f09e}", color: rgb("#f36f24"))
#let fa_github = draw_font_icon("\u{f09b}", color: rgb("#454545"))
#let fa_people = draw_font_icon("\u{f007}", color: color_gray)
#let fa_pencil = draw_font_icon("\u{f044}", color: color_gray)
#let fa_education = draw_font_icon("\u{f19d}", color: color_gray)
#let fa_code = draw_font_icon("\u{f121}", color: color_gray)
#let fa_project = draw_font_icon("\u{f0e8}", color: color_gray)
#let fa_wrench = draw_font_icon("\u{f0ad}", color: color_gray)
#let fa_wechat = draw_font_icon("\u{f1d7}", color: color_light_gray)

#resume(
  "林进森的简历",
  "林进森",
  (x: 1.5cm, y: 1.8cm),
)[
  #text(weight: "bold", size: 22pt)[林进森]
  #v(0.2em)
  #text(size: 14pt, fill: color_gray)[客户端工程师]

  #v(0.6em)

  #stack(
    dir: ltr,
    spacing: 3.8em,
    stack(dir: ltr, spacing: 0.4em, fa_envelop, link("mailto:<EMAIL>")[kesco915\@gmail.com]),
    stack(dir: ltr, spacing: 0.4em, fa_phone, link("tel:18123985415")[18123985415]),
    stack(dir: ltr, spacing: 0.4em, fa_wechat, link("weixin://dl/chat?bringwin808")[bringwin808]),
    stack(dir: ltr, spacing: 0.4em, fa_map_marker, [广东省深圳市]),
  )

  #v(0.8em)
  #line(length: 100%, stroke: 1pt + color_line)
  #v(0.8em)

  #stack(
    spacing: 0.6em,
    stack(
      dir: ltr,
      spacing: 0.4em,
      fa_rss,
      link("http://kescoode.com")[#text(fill: color_blue)[http://kescoode.com]],
    ),
    stack(
      dir: ltr,
      spacing: 0.4em,
      fa_github,
      link("http://github.com/kesco")[#text(fill: color_blue)[http://github.com/kesco]],
    ),
  )

  == #fa_people 个人简介

  拥有10年丰富的客户端开发经验，专注于Android客户端架构优化、跨平台动态渲染框架设计与高性能系统架构。在腾讯任职期间主导并成功交付多个核心业务项目，在性能监控体系建设、动态化引擎架构设计以及跨平台技术解决方案方面积累了深厚的技术沉淀。具备从0到1构建完整技术体系的能力，致力于在跨平台框架技术领域持续创新与深度探索。

  == #fa_pencil 工作经历

  #text(weight: "bold", size: 12pt, fill: color_light_blue)[深圳市腾讯计算机系统有限公司，深圳]，客户端开发（T10）
  #v(0.2em)
  #timeline(datetime(year: 2016, month: 10, day: 1), to: datetime.today(), display_duration: true)
  #v(0.4em)

  先后任职于腾讯PCG事业群社交平台产品部（2016年至2019年）和IEG事业群用户平台部（2019年至今），担任客户端工程师职务，专注于大型互联网产品的客户端架构设计与技术实现。

  #v(0.6em)
  #text(weight: "bold")[IEG事业群用户平台部] #timeline_pin(datetime(year: 2019, month: 10, day: 1), datetime.today())
  #v(0.2em)
  担任游戏知己SDK项目技术负责人，主导Android端SDK的完整技术架构设计与实现。游戏知己作为腾讯游戏AI机器人产品，提供智能客服、个性化推荐等核心功能，已成功接入王者荣耀等多款头部游戏产品。
  #v(0.2em)
  - 主导设计并实现新一代动态化游戏引擎SDK的完整渲染架构方案，实现跨游戏引擎的统一渲染能力
  - 负责Hippy版本SDK在Android平台的性能优化与合规性改造，提升SDK整体稳定性与用户体验
  - 主导构建SDK WebSocket长连接通信架构，完成网络栈的重构与优化，提升连接稳定性
  - 实施SDK插件化架构改造，优化Dex加载性能，显著提升SDK初始化速度
  - 主导VLink SDK国际化项目的性能优化与稳定性提升，支撑海外业务拓展需求

  #v(0.6em)
  #text(weight: "bold")[PCG事业群社交平台产品部] #timeline_pin(
    datetime(year: 2016, month: 10, day: 1),
    datetime(year: 2019, month: 9, day: 30),
  )
  #v(0.2em)
  负责手机QQ、Wtlogin SDK及互联SDK等核心产品的Android端技术开发与架构优化，参与亿级用户产品的基础设施建设。
  #v(0.2em)
  - 主导手机QQ主页面框架与消息页框架的性能优化，实现7.0版本底部导航交互优化及AIO消息文本选择功能
  - 负责互联分享功能组件的架构设计与安全防护体系建设，包括第三方授权管理、动态头像设置及安全攻击防御机制
  - 基于Ark框架设计并实现AIO闪字功能的Lua脚本化架构，提升功能开发效率与动态更新能力
  - 担任手机QQ SP组件技术负责人，建立完善的业务调用监控体系
  - 设计并实现Native内存监控系统，为Debug版本提供内存使用量监控、内存泄漏检测及Double Free问题追踪
  - 主导Wtlogin SDK安全防御体系建设，构建针对第三方应用攻击的完整防护方案

  #v(1.2em)
  #text(weight: "bold", size: 12pt, fill: color_light_blue)[阿里巴巴网络技术有限公司，杭州]，无线开发工程师（P5）
  #v(0.2em)
  #timeline(
    datetime(year: 2016, month: 2, day: 1),
    to: datetime(year: 2016, month: 10, day: 1),
    display_duration: true,
  )
  #v(0.4em)

  任职于阿里巴巴1688技术部Android应用开发组，担任无线开发工程师职务，专注于大型电商平台移动端技术架构优化与中台基础设施建设。

  #text(weight: "bold", size: 10pt)[核心技术贡献：]
  - 主导手机阿里首页动态Native化技术改造，通过ROC组件化架构设计，实现页面渲染性能提升30%
  - 主导商品详情页Hybrid转Native架构升级，采用模块化设计理念，显著提升用户交互体验
  - 负责AliDataBinding MVVM框架在手机阿里多个核心业务场景的技术落地，推动团队从MVP向MVVM架构转型
  - 参与Weex跨平台动态化方案的技术实践，构建完整的组件开发与调试工具链

  #v(1.2em)
  #text(weight: "bold", size: 12pt, fill: color_light_blue)[金蝶国际软件，深圳]，Android工程师
  #v(0.2em)
  #timeline(
    datetime(year: 2014, month: 7, day: 1),
    to: datetime(year: 2016, month: 1, day: 1),
    display_duration: true,
  )
  #v(0.4em)

  任职于金蝶移动互联网事业部，担任Android开发工程师职务，专注于企业级移动办公平台的技术架构设计与系统优化。

  #text(weight: "bold", size: 10pt)[核心技术贡献：]
  - 担任云之家IM即时通讯系统与智能考勤打卡模块的技术架构负责人，设计并实现高并发消息处理机制
  - 主导自研推送系统的Android端技术实现，基于长连接架构设计，实现消息到达率95%以上
  - 主导设计并实现Android 2.3~5.1多版本兼容的后台保活框架，解决不同系统版本的进程保活技术难题
  - 负责客户端网络基础设施的架构设计，构建稳定可靠的HTTP通信框架
  - 建立完整的CI/CD自动化构建体系，实现多渠道包的自动化打包与测试环境部署，构建效率提升80%

  == #fa_project 重点项目

  #text(weight: "bold", size: 12pt, fill: color_light_blue)[游戏知己动态化游戏引擎渲染方案Galatea] #timeline_pin(datetime(year: 2022, month: 10, day: 3), datetime(year: 2023, month: 8, day: 5))
  #v(0.2em)
  针对游戏知己SDK在多游戏引擎环境下的渲染统一性和性能优化需求，担任技术负责人主导设计并实现跨游戏引擎UI Binding框架Galatea，实现统一渲染能力和动态热更新机制。
  #v(0.2em)
  + 主导Galatea框架整体技术架构设计，建立完整的项目管理体系与开发流程规范
  + 设计并实现Lua沙盒环境与上层业务Lua Framework，构建安全可靠的脚本执行环境
  + 开发声明式GUI DSL与MVVM架构框架，提升UI开发效率与代码可维护性
  + 实现基于Unity UGUI和Unreal Slate UI的高性能渲染器，确保跨引擎渲染一致性
  + 构建SDK仿真器与Lua Debugger等完整开发工具链，提升开发调试效率
  + 基于Figma插件实现D2C（Design to Code）自动化功能，显著提升换肤业务开发效率
  #v(0.2em)
  项目成功交付后，新版本知己SDK已接入王者荣耀等多款头部游戏产品，实现内存占用从100M+优化至30M，包体积从13M压缩至1.8M（Android端），同时支持Android/iOS/鸿蒙Next/Windows/macOS/Switch等多平台部署。
  

  #v(0.6em)
  #text(weight: "bold", size: 12pt, fill: color_light_blue)[手机QQ Android Native内存监控Framework] #timeline_pin(datetime(year: 2019, month: 4, day: 10),datetime(year: 2019, month: 7, day: 26))
  #v(0.2em)
  针对手机QQ启动时加载数十个动态库所带来的内存安全风险，担任技术负责人主导设计并实现实时内存监控Framework，建立完整的Native层内存安全保障体系。
  #v(0.2em)
  + 主导监控Framework整体技术架构设计，建立完整的项目管理与质量保障体系
  + 自主研发PLT Hook技术方案，实现C++ Native内存申请与释放的实时监控与调用栈追踪
  + 深度改造unwind库，完成Android平台的兼容性适配与性能优化
  + 设计并实现进程暂停机制与内存DUMP扫描算法，构建高效的内存泄漏检测能力
  #v(0.2em)
  项目于2019年中成功全量上线手机QQ Android端，首月完成150+内部动态库的全面扫描，成功识别并上报18个潜在内存泄漏与double free安全隐患，显著提升应用稳定性。

  #v(0.6em)
  #text(weight: "bold", size: 12pt, fill: color_light_blue)[阿里巴巴Android MVVM框架AliDataBinding研发与业务落地] #timeline_pin(datetime(year: 2016, month: 3, day: 22),datetime(year: 2016, month: 8, day: 1),)
  #v(0.2em)
  针对手机阿里原有MVP架构在复杂业务场景下的维护性与稳定性问题，参与内部MVVM框架AliDataBinding的核心技术研发，并主导相关业务页面的架构升级改造。
  #v(0.2em)
  + 参与AliDataBinding框架View Sync核心模块开发，完成内部自定义组件适配与视图绑定机制优化
  + 主导收藏夹、猜你喜欢等核心业务页面的MVP向MVVM架构重构工作
  + 建立完整的框架测试体系，确保架构迁移过程中的业务稳定性
  #v(0.2em)
  项目成功交付后，相关业务页面Crash率从0.07%显著降低至0.04%，架构可维护性与开发效率得到大幅提升。

  #v(0.6em)
  #text(weight: "bold", size: 12pt, fill: color_light_blue)[云之家自研App长连接推送框架] #timeline_pin(datetime(year: 2015, month: 6, day: 1),datetime(year: 2015, month: 10, day: 2))
  #v(0.2em)
  针对2015年国内Android厂商推送服务不完善导致IM消息到达率偏低的技术痛点，担任推送系统架构设计负责人，主导构建企业级自研长连接推送解决方案。
  #v(0.2em)
  + 设计推送长连接通信协议与安全认证机制，基于Netty框架构建高性能后端Comet层组件
  + 攻克Android 2.3~5.1多版本后台保活技术难题，实现跨版本兼容的Android端SDK网络协议栈
  + 建立弱网环境自动化测试流水线，构建完整的网络环境适应性与服务稳定性保障体系
  #v(0.2em)
  项目成功上线后稳定服务110万企业用户，消息到达率从原有方案的不足70%提升至95%以上，为云之家IM核心功能提供了可靠的技术基础设施支撑。

  == #fa_education 教育经历

  #text(weight: "bold", size: 12pt)[电子信息工程]，深圳大学
  #v(0.2em)
  #timeline(
    datetime(year: 2010, month: 9, day: 10),
    to: datetime(year: 2014, month: 6, day: 28),
    display_duration: false,
  )

  == #fa_code 专业技能

  #text(weight: "bold")[Android开发]
  - 熟悉Android应用开发，深度掌握Google Jetpack架构组件和Kotlin Multiplatform Mobile等主流开源技术栈，具备Android Framework层面的深入理解和实践经验。
  - 具备Android插件化架构设计与实现能力，拥有完整的插件化框架开发经验。
  - 掌握Android底层Hook技术，包括PLT Hook和JNI Hook等Native层调试与性能优化技术。

  #v(0.6em)
  #text(weight: "bold")[跨平台开发]
  - 具备独立自研跨平台GUI框架的完整技术能力，曾主导团队实现支持Android/iOS/鸿蒙Next/Windows/Linux/MacOS等多平台的完整GUI框架解决方案。
  - 具备Weex、Hippy等跨平台动态化框架的深度开发经验，曾为Hippy开源项目贡献优化方案并成功合并。
  - 拥有丰富的跨平台C++开发经验，擅长跨平台差异抹平、高稳定性C++代码编写、Native层内存监控系统设计以及Native Crash追踪机制实现。
  - 具备跨平台Zig语言开发能力，熟练掌握Zig-Clang编译工具链，拥有最小化SDK框架设计与实现的丰富经验。

  #v(0.6em)
  #text(weight: "bold")[鸿蒙开发]
  - 熟悉鸿蒙ArkTS应用开发技术，深入了解鸿蒙SDK API11至API14版本间的技术差异与演进特性。
  - 具备丰富的业务系统向鸿蒙Next平台迁移的实践经验，曾成功主导两款核心SDK在鸿蒙Next平台的技术落地与产品化部署。

  #v(0.6em)
  #text(weight: "bold")[游戏开发]
  - 深入理解Unity UGUI和Unreal Engine Slate UI等游戏引擎底层UI框架的架构设计与实现原理。
  - 具备Unity和Unreal Engine游戏引擎原生插件SDK的完整开发经验，熟悉引擎扩展机制。
  - 熟悉Lua脚本语言，曾主导设计并实现游戏引擎Lua协程框架、声明式GUI DSL、MVVM架构框架、Debugger后端服务以及性能分析工具。
  - 拥有Lua解析器嵌入游戏引擎的完整解决方案实施经验，具备相应Lua Framework脚手架的架构设计与开发能力。

  #v(0.6em)
  #text(weight: "bold")[Web和后端开发]
  - 熟练掌握JavaScript(ES6)及TypeScript语言技术栈，具备Preact.js、Vue.js和Foundation等现代前端框架的深度开发经验。
  - 拥有基于Hippy、Weex等跨平台前端框架的项目开发与性能优化的丰富实践经验。

  #v(0.6em)
  #text(weight: "bold")[DevOps与工程化]
  - 熟悉Jenkins、GitLab CI、GitHub Actions、Fastlane等持续集成工具的流程设计与应用，具备使用Go、Node.js等语言开发项目构建脚本、自动化测试框架和自定义插件的技术能力。
  - 具备Firebase Crashlytics等崩溃监控和日志收集系统的深度应用经验，拥有针对企业级项目设计并实现监控日志系统和埋点上报系统的完整架构设计与开发经验。

]
