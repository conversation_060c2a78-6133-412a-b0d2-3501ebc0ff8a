#let color_gray = rgb("#777777")
#let color_light_gray = rgb("#888888")
#let color_black = rgb("#333333")
#let color_line = rgb("#eeeeee")
#let color_light_blue = rgb(66, 139, 202)
#let color_blue = rgb(42, 100, 150)

#let draw_font_icon(unicode, color: rgb("#888"), size: 1em, solid: true) = {
  text(
    font: if solid {
      "Font Awesome 6 Free Solid"
    } else {
      "Font Awesome 6 Free"
    },
    weight: "black",
    size: size,
    fill: color,
    unicode,
  )
}

#let fa_clock = draw_font_icon("\u{f017}", color: color_gray, size: 10pt, solid: false)

#let timeline(from, to: datetime.today(), display_duration: false) = {
  let isToday = to == datetime.today()

  if display_duration {
    let total_months = (to.year() - from.year()) * 12 + to.month() - from.month()
    let years = calc.floor(total_months / 12)
    let months = total_months - (years * 12)
    let suffix = if (years > 0) {
      if months < 10 {
        str(years) + "年0" + str(months) + "个月"
      } else {
        str(years) + "年" + str(months) + "个月"
      }
    } else {
      if months < 10 {
        "0" + str(months) + "个月"
      } else {
        str(months) + "个月"
      }
    }

    if isToday {
      text(
        style: "italic",
        fill: color_light_gray,
        size: 10pt,
      )[#from.display("[year]年[month]月至今  ") #fa_clock #suffix]
    } else {
      text(
        style: "italic",
        fill: color_light_gray,
        size: 10pt,
      )[#from.display("[year]年[month]月 ") - #to.display(" [year]年[month]月  ") #fa_clock #suffix]
    }
  } else {
    if isToday {
      text(style: "italic", fill: color_light_gray, size: 10pt)[#from.display("[year]年[month]月至今")]
    } else {
      text(
        style: "italic",
        fill: color_light_gray,
        size: 10pt,
      )[#from.display("[year]年[month]月 - ") #to.display("[year]年[month]月")]
    }
  }
}

#let timeline_pin(from, to) = {
  if to == datetime.today() {
    text(style: "italic", fill: color_light_gray, size: 10pt)[#fa_clock #from.display("[year]年[month]月至今")]
  } else {
    text(style: "italic", fill: color_light_gray, size: 10pt)[#fa_clock #from.display("[year]年[month]月 - ") #to.display("[year]年[month]月")]
  }
}

#let resume(title, author, margin, body) = {
  set document(title: title, author: author, keywords: "resume, cv")

  set page(paper: "a4", margin: margin, numbering: "第1页")
  set text(font: "LXGW Bright", size: 11pt, fill: color_black)

  // 二级标题下加一条横线
  show heading.where(level: 2): it => stack(
    v(1.2em),
    text(
      weight: "bold",
      size: 14pt,
      fill: color_gray,
      it,
    ),
    v(1em),
    line(length: 100%, stroke: 1pt + color_line),
    v(1em),
  )

  body
}
